# Undici Keep-Alive Performance Test

Comprehensive test suite để kiểm tra và so sánh hiệu suất của HTTP keep-alive connections trong thư viện undici.

## Mục tiêu

- ✅ Xác minh undici có hỗ trợ HTTP keep-alive connection pooling chính xác
- ✅ So sánh hiệu suất giữa keep-alive enabled vs disabled
- ✅ Đo lường và log metrics để chứng minh connection reuse
- ✅ Cung cấp evidence cho việc tối ưu RPC proxy performance

## Cách chạy test

### 1. Chạy test cơ bản

```bash
# Sử dụng TypeScript trực tiếp
node --import @swc-node/register/esm-register test/test-undici-keep-alive.ts

# Hoặc sử dụng runner script
node test/run-keep-alive-test.js
```

### 2. Chạy test với custom parameters

```bash
# 20 requests với delay 1 giây
node test/run-keep-alive-test.js --requests=20 --delay=1000

# Sử dụng endpoint kh<PERSON>c
node test/run-keep-alive-test.js --endpoint=https://api.github.com

# Xem help
node test/run-keep-alive-test.js --help
```

### 3. Chạy test trong development environment

```bash
# Sử dụng npm script (nếu có)
npm run test:keep-alive

# Hoặc với pnpm
pnpm test:keep-alive
```

## Test Scenarios

### Scenario 1: Keep-Alive Enabled

- **Keep-Alive Timeout**: 30,000ms (30 giây)
- **Max Keep-Alive Timeout**: 600,000ms (10 phút)
- **Expected**: Connection reuse, ít new connections, response time nhanh hơn

### Scenario 2: Keep-Alive Disabled

- **Keep-Alive Timeout**: 1ms (effectively disabled)
- **Expected**: Mỗi request tạo new connection, không có reuse

## Metrics được đo lường

| Metric                    | Mô tả                             | Ý nghĩa                         |
| ------------------------- | --------------------------------- | ------------------------------- |
| **Total Requests**        | Tổng số requests được gửi         | Baseline cho tính toán          |
| **New Connections**       | Số connections mới được tạo       | Càng ít càng tốt với keep-alive |
| **Connection Reuses**     | Số lần reuse existing connections | Càng nhiều càng tốt             |
| **Average Response Time** | Thời gian response trung bình     | Keep-alive should be faster     |
| **Connection Reuse Rate** | Tỷ lệ % connection được reuse     | Target: >80% với keep-alive     |
| **Connection Efficiency** | Requests per connection           | Higher = better efficiency      |

## Expected Results

### ✅ Successful Keep-Alive Implementation

**Actual Test Results (httpbin.org endpoint):**

```
📊 PERFORMANCE COMPARISON
================================================================================
| Metric                    | Keep-Alive      | No Keep-Alive   | Improvement     |
|---------------------------|-----------------|-----------------|-----------------|
| Avg Response Time         |     673.40ms    |    1981.80ms    |       66.0%     |
| New Connections           |               1 |               5 |       80.0%     |
| Connection Reuse Rate     |        80.0%    |         0.0%    |       80.0%     |
| Connection Efficiency     |            5.00 |            1.00 |      400.0%     |

🎯 CONCLUSION:
✅ Keep-alive provides 66.0% faster response times
✅ Keep-alive reduces new connections by 80.0%
✅ Keep-alive achieves 80.0% connection reuse rate
```

**Key Observations:**

- First request takes ~2000ms (connection establishment + DNS lookup)
- Subsequent requests with keep-alive: ~300ms (connection reuse)
- Without keep-alive: Every request takes ~2000ms (new connection each time)
- **66% performance improvement** with keep-alive enabled

### ❌ Problematic Results (cần investigation)

```
🎯 CONCLUSION:
❌ Keep-alive shows 5.2% slower response times
❌ Keep-alive connection reuse is not working effectively
```

## Troubleshooting

### Common Issues

1. **ENOTFOUND / ECONNREFUSED**

    ```
    💡 Tip: Check if the endpoint is accessible and supports the required HTTP methods
    ```

2. **No connection reuse detected**

    - Kiểm tra endpoint có support keep-alive không
    - Thử với endpoint khác (httpbin.org, api.github.com)
    - Tăng delay giữa requests

3. **Inconsistent results**
    - Network latency có thể ảnh hưởng
    - Chạy test nhiều lần để lấy average
    - Sử dụng local test server để stable results

### Debug Mode

Để xem chi tiết connection events:

```bash
DEBUG=undici* node test/run-keep-alive-test.js
```

## Integration với Project

Test này có thể được integrate vào CI/CD pipeline:

```yaml
# .github/workflows/performance-test.yml
- name: Run Keep-Alive Performance Test
  run: |
      node test/run-keep-alive-test.js --requests=5 --delay=200
```

## Customization

### Sử dụng Local Test Server

```typescript
// Tạo simple HTTP server cho testing
import { createServer } from 'http'

const server = createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({ message: 'test response', timestamp: Date.now() }))
})

server.listen(3000, () => {
    console.log('Test server running on http://localhost:3000')
})

// Chạy test với local server
const test = new UndiciKeepAliveTest('http://localhost:3000')
```

### Custom Metrics

Extend `UndiciKeepAliveTest` class để thêm custom metrics:

```typescript
class ExtendedKeepAliveTest extends UndiciKeepAliveTest {
    private memoryUsage: number[] = []

    protected async makeRequest(pool: Pool, path: string): Promise<number> {
        const memBefore = process.memoryUsage().heapUsed
        const responseTime = await super.makeRequest(pool, path)
        const memAfter = process.memoryUsage().heapUsed

        this.memoryUsage.push(memAfter - memBefore)
        return responseTime
    }
}
```

## Best Practices

1. **Chạy test trong stable network environment**
2. **Sử dụng consistent endpoint** (httpbin.org recommended)
3. **Monitor system resources** during test
4. **Run multiple iterations** để lấy reliable results
5. **Document results** cho performance baseline

## Contributing

Khi modify test:

1. Maintain backward compatibility
2. Add proper TypeScript types
3. Update documentation
4. Test với multiple endpoints
5. Follow project coding conventions
