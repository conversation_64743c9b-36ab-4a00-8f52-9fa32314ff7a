import { createServer, type IncomingMessage, type Server, type ServerResponse } from 'node:http'

interface TestServerOptions {
    port?: number
    keepAliveTimeout?: number
    headersTimeout?: number
}

export class TestServer {
    protected readonly server: Server
    protected readonly port: number
    protected readonly keepAliveTimeout: number

    public constructor(options: TestServerOptions = {}) {
        this.port = options.port || 0
        this.keepAliveTimeout = options.keepAliveTimeout || 5000
        
        this.server = createServer(this.handleRequest.bind(this))
        this.server.keepAliveTimeout = this.keepAliveTimeout
        this.server.headersTimeout = options.headersTimeout || 60000
    }

    public async start() {
        return new Promise<void>((resolve, reject) => {
            this.server.listen(this.port, (error?: Error) => {
                if (error) {
                    reject(error)
                    return
                }
                resolve()
            })
        })
    }

    public async stop() {
        return new Promise<void>((resolve) => {
            this.server.close(() => resolve())
        })
    }

    public getPort() {
        const address = this.server.address()
        if (typeof address === 'object' && address !== null) {
            return address.port
        }
        return this.port
    }

    public getUrl() {
        return `http://localhost:${this.getPort()}`
    }

    protected handleRequest(req: IncomingMessage, res: ServerResponse) {
        const method = req.method?.toUpperCase()
        const url = req.url || '/'
        const timestamp = Date.now()

        res.setHeader('Content-Type', 'application/json')
        res.setHeader('Connection', 'keep-alive')
        res.setHeader('Keep-Alive', `timeout=${Math.floor(this.keepAliveTimeout / 1000)}`)

        if (method === 'GET' && url === '/get') {
            const responseData = {
                method: 'GET',
                url: '/get',
                timestamp,
                headers: req.headers,
                keepAliveTimeout: this.keepAliveTimeout,
                serverTime: new Date().toISOString(),
            }
            
            res.writeHead(200)
            res.end(JSON.stringify(responseData))
            return
        }

        if (method === 'HEAD' && url === '/head') {
            res.setHeader('X-Timestamp', timestamp.toString())
            res.setHeader('X-Keep-Alive-Timeout', this.keepAliveTimeout.toString())
            res.writeHead(200)
            res.end()
            return
        }

        res.writeHead(404)
        res.end(JSON.stringify({ error: 'Not Found', timestamp }))
    }
}
