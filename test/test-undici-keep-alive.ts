import { Pool } from 'undici'

interface TestMetrics {
    totalRequests: number
    newConnections: number
    connectionReuses: number
    totalResponseTime: number
    averageResponseTime: number
    reuseRate: number
    efficiency: number
}

interface PoolStats {
    connected: number
    free: number
    pending: number
    queued: number
    running: number
    size: number
}

class UndiciKeepAliveTest {
    private keepAlivePool: Pool
    private noKeepAlivePool: Pool
    private testEndpoint: string

    constructor(testEndpoint = 'https://httpbin.org') {
        this.testEndpoint = testEndpoint
        
        // Pool with keep-alive enabled
        this.keepAlivePool = new Pool(testEndpoint, {
            keepAliveTimeout: 30000,        // 30 seconds keep-alive
            keepAliveMaxTimeout: 600000,    // 10 minutes max
            connections: 10,                // Max concurrent connections
            pipelining: 1,                  // HTTP pipelining disabled for clarity
            headersTimeout: 30000,          // 30s header timeout
            bodyTimeout: 30000              // 30s body timeout
        })

        // Pool with keep-alive disabled (short timeout to force new connections)
        this.noKeepAlivePool = new Pool(testEndpoint, {
            keepAliveTimeout: 1,            // 1ms - effectively disabled
            keepAliveMaxTimeout: 1,         // 1ms max
            connections: 10,
            pipelining: 1,
            headersTimeout: 30000,
            bodyTimeout: 30000
        })

        this.setupEventListeners()
    }

    private setupEventListeners(): void {
        // Monitor keep-alive pool connections
        this.keepAlivePool.on('connect', (origin) => {
            console.log(`🔗 [KEEP-ALIVE] New connection established to ${origin}`)
        })

        this.keepAlivePool.on('disconnect', (origin, targets, error) => {
            console.log(`❌ [KEEP-ALIVE] Connection disconnected from ${origin}`)
            if (error) console.log(`   Error: ${error.message}`)
        })

        // Monitor no-keep-alive pool connections
        this.noKeepAlivePool.on('connect', (origin) => {
            console.log(`🔗 [NO-KEEP-ALIVE] New connection established to ${origin}`)
        })

        this.noKeepAlivePool.on('disconnect', (origin, targets, error) => {
            console.log(`❌ [NO-KEEP-ALIVE] Connection disconnected from ${origin}`)
            if (error) console.log(`   Error: ${error.message}`)
        })
    }

    private async makeRequest(pool: Pool, path: string): Promise<number> {
        const startTime = Date.now()
        
        try {
            const response = await pool.request({
                path,
                method: 'GET',
                headers: {
                    'User-Agent': 'undici-keep-alive-test/1.0.0'
                }
            })

            // Consume response body to complete the request
            await response.body.text()
            
            return Date.now() - startTime
        } catch (error) {
            console.error(`❌ Request failed:`, error)
            throw error
        }
    }

    private calculateMetrics(
        responseTimes: number[],
        initialStats: PoolStats,
        finalStats: PoolStats,
        connectionEvents: number
    ): TestMetrics {
        const totalRequests = responseTimes.length
        const totalResponseTime = responseTimes.reduce((sum, time) => sum + time, 0)
        const averageResponseTime = totalResponseTime / totalRequests
        
        // Estimate connection reuses based on stats difference
        const maxConnectionsUsed = Math.max(initialStats.connected, finalStats.connected)
        const connectionReuses = Math.max(0, totalRequests - connectionEvents)
        const reuseRate = totalRequests > 0 ? (connectionReuses / totalRequests) * 100 : 0
        const efficiency = connectionEvents > 0 ? totalRequests / connectionEvents : 0

        return {
            totalRequests,
            newConnections: connectionEvents,
            connectionReuses,
            totalResponseTime,
            averageResponseTime,
            reuseRate,
            efficiency
        }
    }

    private logPoolStats(label: string, stats: PoolStats): void {
        console.log(`📊 [${label}] Pool Stats:`)
        console.log(`   Connected: ${stats.connected}`)
        console.log(`   Free: ${stats.free}`)
        console.log(`   Pending: ${stats.pending}`)
        console.log(`   Running: ${stats.running}`)
        console.log(`   Size: ${stats.size}`)
    }

    private logTestResults(scenario: string, metrics: TestMetrics): void {
        console.log(`\n📈 [${scenario}] Test Results:`)
        console.log(`   Total Requests: ${metrics.totalRequests}`)
        console.log(`   New Connections: ${metrics.newConnections}`)
        console.log(`   Connection Reuses: ${metrics.connectionReuses}`)
        console.log(`   Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`)
        console.log(`   Total Response Time: ${metrics.totalResponseTime}ms`)
        console.log(`   Connection Reuse Rate: ${metrics.reuseRate.toFixed(1)}%`)
        console.log(`   Connection Efficiency: ${metrics.efficiency.toFixed(2)} requests/connection`)
    }

    private logComparison(keepAliveMetrics: TestMetrics, noKeepAliveMetrics: TestMetrics): void {
        console.log('\n' + '='.repeat(80))
        console.log('📊 PERFORMANCE COMPARISON')
        console.log('='.repeat(80))
        
        const performanceImprovement = ((noKeepAliveMetrics.averageResponseTime - keepAliveMetrics.averageResponseTime) / noKeepAliveMetrics.averageResponseTime) * 100
        const connectionReduction = ((noKeepAliveMetrics.newConnections - keepAliveMetrics.newConnections) / noKeepAliveMetrics.newConnections) * 100

        console.log(`| Metric                    | Keep-Alive      | No Keep-Alive   | Improvement     |`)
        console.log(`|---------------------------|-----------------|-----------------|-----------------|`)
        console.log(`| Avg Response Time         | ${keepAliveMetrics.averageResponseTime.toFixed(2).padStart(10)}ms    | ${noKeepAliveMetrics.averageResponseTime.toFixed(2).padStart(10)}ms    | ${performanceImprovement.toFixed(1).padStart(10)}%     |`)
        console.log(`| New Connections           | ${keepAliveMetrics.newConnections.toString().padStart(15)} | ${noKeepAliveMetrics.newConnections.toString().padStart(15)} | ${connectionReduction.toFixed(1).padStart(10)}%     |`)
        console.log(`| Connection Reuse Rate     | ${keepAliveMetrics.reuseRate.toFixed(1).padStart(12)}%    | ${noKeepAliveMetrics.reuseRate.toFixed(1).padStart(12)}%    | ${(keepAliveMetrics.reuseRate - noKeepAliveMetrics.reuseRate).toFixed(1).padStart(10)}%     |`)
        console.log(`| Connection Efficiency     | ${keepAliveMetrics.efficiency.toFixed(2).padStart(15)} | ${noKeepAliveMetrics.efficiency.toFixed(2).padStart(15)} | ${((keepAliveMetrics.efficiency - noKeepAliveMetrics.efficiency) / noKeepAliveMetrics.efficiency * 100).toFixed(1).padStart(10)}%     |`)
        
        console.log('\n🎯 CONCLUSION:')
        if (performanceImprovement > 0) {
            console.log(`✅ Keep-alive provides ${performanceImprovement.toFixed(1)}% faster response times`)
        } else {
            console.log(`❌ Keep-alive shows ${Math.abs(performanceImprovement).toFixed(1)}% slower response times`)
        }
        
        if (connectionReduction > 0) {
            console.log(`✅ Keep-alive reduces new connections by ${connectionReduction.toFixed(1)}%`)
        } else {
            console.log(`❌ Keep-alive increases new connections by ${Math.abs(connectionReduction).toFixed(1)}%`)
        }
        
        if (keepAliveMetrics.reuseRate > noKeepAliveMetrics.reuseRate) {
            console.log(`✅ Keep-alive achieves ${keepAliveMetrics.reuseRate.toFixed(1)}% connection reuse rate`)
        } else {
            console.log(`❌ Keep-alive connection reuse is not working effectively`)
        }
    }

    public async runTest(requestCount = 10, delayBetweenRequests = 500): Promise<void> {
        console.log('🚀 Starting Undici Keep-Alive Performance Test')
        console.log(`📋 Test Configuration:`)
        console.log(`   Endpoint: ${this.testEndpoint}`)
        console.log(`   Request Count: ${requestCount}`)
        console.log(`   Delay Between Requests: ${delayBetweenRequests}ms`)
        console.log(`   Keep-Alive Timeout: 30000ms`)
        console.log(`   No Keep-Alive Timeout: 1ms`)
        console.log('')

        // Test with keep-alive enabled
        console.log('🔄 Testing with Keep-Alive ENABLED...')
        const keepAliveResults = await this.runScenario(
            this.keepAlivePool,
            'KEEP-ALIVE',
            requestCount,
            delayBetweenRequests
        )

        // Wait a bit between tests to ensure connections are closed
        console.log('\n⏳ Waiting 2 seconds between tests...')
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Test with keep-alive disabled
        console.log('\n🔄 Testing with Keep-Alive DISABLED...')
        const noKeepAliveResults = await this.runScenario(
            this.noKeepAlivePool,
            'NO-KEEP-ALIVE',
            requestCount,
            delayBetweenRequests
        )

        // Compare results
        this.logComparison(keepAliveResults, noKeepAliveResults)
    }

    private async runScenario(
        pool: Pool,
        label: string,
        requestCount: number,
        delay: number
    ): Promise<TestMetrics> {
        const responseTimes: number[] = []
        let connectionEvents = 0
        
        // Track connection events
        const connectionHandler = () => connectionEvents++
        pool.on('connect', connectionHandler)
        
        const initialStats = { ...pool.stats }
        this.logPoolStats(`${label} INITIAL`, initialStats)

        // Make requests with delay
        for (let i = 1; i <= requestCount; i++) {
            console.log(`📤 [${label}] Making request ${i}/${requestCount}...`)
            
            try {
                const responseTime = await this.makeRequest(pool, '/get')
                responseTimes.push(responseTime)
                
                const currentStats = pool.stats
                console.log(`✅ [${label}] Request ${i} completed in ${responseTime}ms (Free connections: ${currentStats.free})`)
                
                if (i < requestCount) {
                    await new Promise(resolve => setTimeout(resolve, delay))
                }
            } catch (error) {
                console.error(`❌ [${label}] Request ${i} failed:`, error)
            }
        }

        const finalStats = { ...pool.stats }
        this.logPoolStats(`${label} FINAL`, finalStats)

        // Remove event listener
        pool.off('connect', connectionHandler)

        const metrics = this.calculateMetrics(responseTimes, initialStats, finalStats, connectionEvents)
        this.logTestResults(label, metrics)

        return metrics
    }

    public async cleanup(): Promise<void> {
        console.log('\n🧹 Cleaning up resources...')
        await Promise.all([
            this.keepAlivePool.close(),
            this.noKeepAlivePool.close()
        ])
        console.log('✅ Cleanup completed')
    }
}

// Main execution function
async function main(): Promise<void> {
    const test = new UndiciKeepAliveTest()
    
    try {
        await test.runTest(10, 500) // 10 requests with 500ms delay
    } catch (error) {
        console.error('❌ Test failed:', error)
        process.exit(1)
    } finally {
        await test.cleanup()
    }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error)
}

export { UndiciKeepAliveTest }
