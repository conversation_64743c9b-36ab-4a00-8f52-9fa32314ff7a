import { Pool } from 'undici'
import { TestServer } from './test-server'

interface TestMetrics {
    totalRequests: number
    newConnections: number
    connectionReuses: number
    totalResponseTime: number
    averageResponseTime: number
    reuseRate: number
    efficiency: number
}

interface PoolStats {
    connected: number
    free: number
    pending: number
    queued: number
    running: number
    size: number
}

class UndiciKeepAliveTest {
    protected readonly testServer: TestServer
    protected keepAlivePool!: Pool
    protected noKeepAlivePool!: Pool
    protected expirationPool!: Pool
    protected maintenancePool!: Pool
    protected testEndpoint: string

    public constructor(serverKeepAliveTimeout = 5000) {
        this.testServer = new TestServer({ keepAliveTimeout: serverKeepAliveTimeout })
        this.testEndpoint = ''
    }

    protected setupEventListeners() {
        this.keepAlivePool.on('connect', (origin) => {
            console.log(`🔗 [KEEP-ALIVE] New connection established to ${origin}`)
        })

        this.keepAlivePool.on('disconnect', (origin, _targets, error) => {
            console.log(`❌ [KEEP-ALIVE] Connection disconnected from ${origin}`)

            if (error) { console.log(`   Error: ${error.message}`) }
        })

        this.noKeepAlivePool.on('connect', (origin) => {
            console.log(`🔗 [NO-KEEP-ALIVE] New connection established to ${origin}`)
        })

        this.noKeepAlivePool.on('disconnect', (origin, _targets, error) => {
            console.log(`❌ [NO-KEEP-ALIVE] Connection disconnected from ${origin}`)

            if (error) { console.log(`   Error: ${error.message}`) }
        })

        this.expirationPool.on('connect', (origin) => {
            console.log(`🔗 [EXPIRATION] New connection established to ${origin}`)
        })

        this.expirationPool.on('disconnect', (origin, _targets, error) => {
            console.log(`❌ [EXPIRATION] Connection disconnected from ${origin}`)

            if (error) { console.log(`   Error: ${error.message}`) }
        })

        this.maintenancePool.on('connect', (origin) => {
            console.log(`🔗 [MAINTENANCE] New connection established to ${origin}`)
        })

        this.maintenancePool.on('disconnect', (origin, _targets, error) => {
            console.log(`❌ [MAINTENANCE] Connection disconnected from ${origin}`)

            if (error) { console.log(`   Error: ${error.message}`) }
        })
    }

    public async startServer() {
        await this.testServer.start()
        this.testEndpoint = this.testServer.getUrl()

        this.keepAlivePool = new Pool(this.testEndpoint, {
            keepAliveTimeout: 30_000,
            keepAliveMaxTimeout: 600_000,
            connections: 10,
            pipelining: 1,
            headersTimeout: 30_000,
            bodyTimeout: 30_000,
        })

        this.noKeepAlivePool = new Pool(this.testEndpoint, {
            keepAliveTimeout: 1,
            keepAliveMaxTimeout: 1,
            connections: 10,
            pipelining: 1,
            headersTimeout: 30_000,
            bodyTimeout: 30_000,
        })

        this.expirationPool = new Pool(this.testEndpoint, {
            keepAliveTimeout: 30_000,
            keepAliveMaxTimeout: 600_000,
            connections: 10,
            pipelining: 1,
            headersTimeout: 30_000,
            bodyTimeout: 30_000,
        })

        this.maintenancePool = new Pool(this.testEndpoint, {
            keepAliveTimeout: 30_000,
            keepAliveMaxTimeout: 600_000,
            connections: 10,
            pipelining: 1,
            headersTimeout: 30_000,
            bodyTimeout: 30_000,
        })

        this.setupEventListeners()
    }

    protected async makeRequest(pool: Pool, path: string, method = 'GET') {
        const startTime = Date.now()

        try {
            const response = await pool.request({
                path,
                method,
                headers: {
                    'User-Agent': 'undici-keep-alive-test/1.0.0',
                    'Connection': 'keep-alive',
                },
            })

            if (method === 'GET') {
                await response.body.text()
            }

            return Date.now() - startTime
        } catch (error) {
            console.error(`❌ Request failed:`, error)
            throw error
        }
    }

    protected calculateMetrics(responseTimes: number[], _initialStats: PoolStats, _finalStats: PoolStats, connectionEvents: number) {
        const totalRequests = responseTimes.length
        const totalResponseTime = responseTimes.reduce((sum, time) => sum + time, 0)
        const averageResponseTime = totalResponseTime / totalRequests
        const connectionReuses = Math.max(0, totalRequests - connectionEvents)
        const reuseRate = totalRequests > 0 ? (connectionReuses / totalRequests) * 100 : 0
        const efficiency = connectionEvents > 0 ? totalRequests / connectionEvents : 0

        return {
            totalRequests,
            newConnections: connectionEvents,
            connectionReuses,
            totalResponseTime,
            averageResponseTime,
            reuseRate,
            efficiency,
        }
    }

    protected logPoolStats(label: string, stats: PoolStats) {
        console.log(`📊 [${label}] Pool Stats:`)
        console.log(`   Connected: ${stats.connected}`)
        console.log(`   Free: ${stats.free}`)
        console.log(`   Pending: ${stats.pending}`)
        console.log(`   Running: ${stats.running}`)
        console.log(`   Size: ${stats.size}`)
    }

    protected logTestResults(scenario: string, metrics: TestMetrics) {
        console.log(`\n📈 [${scenario}] Test Results:`)
        console.log(`   Total Requests: ${metrics.totalRequests}`)
        console.log(`   New Connections: ${metrics.newConnections}`)
        console.log(`   Connection Reuses: ${metrics.connectionReuses}`)
        console.log(`   Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`)
        console.log(`   Total Response Time: ${metrics.totalResponseTime}ms`)
        console.log(`   Connection Reuse Rate: ${metrics.reuseRate.toFixed(1)}%`)
        console.log(`   Connection Efficiency: ${metrics.efficiency.toFixed(2)} requests/connection`)
    }

    protected logComparison(scenarios: Array<{ label: string, metrics: TestMetrics }>) {
        console.log(`\n${'='.repeat(100)}`)
        console.log('📊 PERFORMANCE COMPARISON')
        console.log('='.repeat(100))

        const headers = ['Metric', ...scenarios.map((s) => s.label)]
        const colWidth = 20

        console.log(`| ${headers.map((h) => h.padEnd(colWidth - 1)).join('| ')}|`)
        console.log(`|${headers.map(() => '-'.repeat(colWidth)).join('|')}|`)

        const metrics = [
            { label: 'Avg Response Time', getValue: (m: TestMetrics) => `${m.averageResponseTime.toFixed(2)}ms` },
            { label: 'New Connections', getValue: (m: TestMetrics) => m.newConnections.toString() },
            { label: 'Connection Reuse Rate', getValue: (m: TestMetrics) => `${m.reuseRate.toFixed(1)}%` },
            { label: 'Connection Efficiency', getValue: (m: TestMetrics) => m.efficiency.toFixed(2) },
        ]

        for (const metric of metrics) {
            const values = scenarios.map((s) => metric.getValue(s.metrics))
            console.log(`| ${metric.label.padEnd(colWidth - 1)}| ${values.map((v) => v.padEnd(colWidth - 1)).join('| ')}|`)
        }

        console.log('\n🎯 SCENARIO ANALYSIS:')

        for (const scenario of scenarios) {
            const m = scenario.metrics
            console.log(`${scenario.label}: ${m.reuseRate.toFixed(1)}% reuse rate, ${m.averageResponseTime.toFixed(2)}ms avg response`)
        }
    }

    public async runTest(requestCount = 10, delayBetweenRequests = 500) {
        await this.startServer()

        console.log('🚀 Starting Undici Keep-Alive Performance Test')
        console.log(`📋 Test Configuration:`)
        console.log(`   Endpoint: ${this.testEndpoint}`)
        console.log(`   Request Count: ${requestCount}`)
        console.log(`   Delay Between Requests: ${delayBetweenRequests}ms`)
        console.log(`   Server Keep-Alive Timeout: 5000ms`)
        console.log('')

        const scenarios: Array<{ label: string, metrics: TestMetrics }> = []

        console.log('🔄 Testing Standard Keep-Alive...')
        const keepAliveResults = await this.runStandardScenario(requestCount, delayBetweenRequests)
        scenarios.push({ label: 'Standard Keep-Alive', metrics: keepAliveResults })

        console.log('\n⏳ Waiting 2 seconds between tests...')
        await new Promise((resolve) => setTimeout(resolve, 2000))

        console.log('\n🔄 Testing No Keep-Alive...')
        const noKeepAliveResults = await this.runNoKeepAliveScenario(requestCount, delayBetweenRequests)
        scenarios.push({ label: 'No Keep-Alive', metrics: noKeepAliveResults })

        console.log('\n⏳ Waiting 2 seconds between tests...')
        await new Promise((resolve) => setTimeout(resolve, 2000))

        console.log('\n🔄 Testing Keep-Alive Expiration...')
        const expirationResults = await this.runExpirationScenario()
        scenarios.push({ label: 'Keep-Alive Expiration', metrics: expirationResults })

        console.log('\n⏳ Waiting 2 seconds between tests...')
        await new Promise((resolve) => setTimeout(resolve, 2000))

        console.log('\n🔄 Testing Keep-Alive Maintenance...')
        const maintenanceResults = await this.runMaintenanceScenario()
        scenarios.push({ label: 'Keep-Alive Maintenance', metrics: maintenanceResults })

        this.logComparison(scenarios)
    }

    protected async runStandardScenario(requestCount: number, delay: number) {
        return this.runScenario(this.keepAlivePool, 'STANDARD', requestCount, delay)
    }

    protected async runNoKeepAliveScenario(requestCount: number, delay: number) {
        return this.runScenario(this.noKeepAlivePool, 'NO-KEEP-ALIVE', requestCount, delay)
    }

    protected async runExpirationScenario() {
        console.log('📋 Testing keep-alive expiration after server timeout...')
        console.log('   Making initial request...')

        const responseTimes: number[] = []
        let connectionEvents = 0

        const connectionHandler = () => connectionEvents++
        this.expirationPool.on('connect', connectionHandler)

        const initialStats = { ...this.expirationPool.stats }

        const firstResponse = await this.makeRequest(this.expirationPool, '/get')
        responseTimes.push(firstResponse)
        console.log(`   ✅ First request completed in ${firstResponse}ms`)

        console.log('   Waiting 6 seconds for server keep-alive timeout...')
        await new Promise((resolve) => setTimeout(resolve, 6000))

        console.log('   Making second request after timeout...')
        const secondResponse = await this.makeRequest(this.expirationPool, '/get')
        responseTimes.push(secondResponse)
        console.log(`   ✅ Second request completed in ${secondResponse}ms`)

        const finalStats = { ...this.expirationPool.stats }
        this.expirationPool.off('connect', connectionHandler)

        return this.calculateMetrics(responseTimes, initialStats, finalStats, connectionEvents)
    }

    protected async runMaintenanceScenario() {
        console.log('📋 Testing keep-alive maintenance with periodic GET requests...')

        const responseTimes: number[] = []
        let connectionEvents = 0

        const connectionHandler = () => connectionEvents++
        this.maintenancePool.on('connect', connectionHandler)

        const initialStats = { ...this.maintenancePool.stats }

        console.log('   Making initial GET request...')
        const firstResponse = await this.makeRequest(this.maintenancePool, '/get')
        responseTimes.push(firstResponse)
        console.log(`   ✅ Initial request completed in ${firstResponse}ms`)

        for (let i = 1; i <= 3; i++) {
            console.log(`   Waiting 3 seconds, then sending maintenance GET request ${i}/3...`)
            await new Promise((resolve) => setTimeout(resolve, 3000))

            const maintenanceResponse = await this.makeRequest(this.maintenancePool, '/get', 'GET')
            console.log(`   ✅ Maintenance request ${i} completed in ${maintenanceResponse}ms`)
        }

        console.log('   Making final GET request...')
        const finalResponse = await this.makeRequest(this.maintenancePool, '/get')
        responseTimes.push(finalResponse)
        console.log(`   ✅ Final request completed in ${finalResponse}ms`)

        const finalStats = { ...this.maintenancePool.stats }
        this.maintenancePool.off('connect', connectionHandler)

        return this.calculateMetrics(responseTimes, initialStats, finalStats, connectionEvents)
    }

    protected async runScenario(pool: Pool, label: string, requestCount: number, delay: number) {
        const responseTimes: number[] = []
        let connectionEvents = 0

        const connectionHandler = () => connectionEvents++
        pool.on('connect', connectionHandler)

        const initialStats = { ...pool.stats }
        this.logPoolStats(`${label} INITIAL`, initialStats)

        for (let i = 1; i <= requestCount; i++) {
            console.log(`📤 [${label}] Making request ${i}/${requestCount}...`)

            try {
                const responseTime = await this.makeRequest(pool, '/get')
                responseTimes.push(responseTime)

                const currentStats = pool.stats
                console.log(`✅ [${label}] Request ${i} completed in ${responseTime}ms (Free connections: ${currentStats.free})`)

                if (i < requestCount) {
                    await new Promise((resolve) => setTimeout(resolve, delay))
                }
            } catch (error) {
                console.error(`❌ [${label}] Request ${i} failed:`, error)
            }
        }

        const finalStats = { ...pool.stats }
        this.logPoolStats(`${label} FINAL`, finalStats)

        pool.off('connect', connectionHandler)

        const metrics = this.calculateMetrics(responseTimes, initialStats, finalStats, connectionEvents)
        this.logTestResults(label, metrics)

        return metrics
    }

    public async cleanup() {
        console.log('\n🧹 Cleaning up resources...')

        await Promise.all([
            this.keepAlivePool.close(),
            this.noKeepAlivePool.close(),
            this.expirationPool.close(),
            this.maintenancePool.close(),
            this.testServer.stop(),
        ])

        console.log('✅ Cleanup completed')
    }
}

async function main() {
    const test = new UndiciKeepAliveTest()

    try {
        await test.runTest(10, 500)
    } catch (error) {
        console.error('❌ Test failed:', error)
        process.exit(1)
    } finally {
        await test.cleanup()
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error)
}

export { UndiciKeepAliveTest }
