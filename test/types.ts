/**
 * Type definitions for Undici Keep-Alive Test Suite
 */

export interface TestMetrics {
    /** Total number of requests made */
    totalRequests: number
    
    /** Number of new connections established */
    newConnections: number
    
    /** Number of connection reuses */
    connectionReuses: number
    
    /** Total response time for all requests */
    totalResponseTime: number
    
    /** Average response time per request */
    averageResponseTime: number
    
    /** Percentage of requests that reused connections */
    reuseRate: number
    
    /** Average requests per connection */
    efficiency: number
}

export interface PoolStats {
    /** Number of active connections */
    connected: number
    
    /** Number of free keep-alive connections */
    free: number
    
    /** Number of pending requests */
    pending: number
    
    /** Number of queued requests */
    queued: number
    
    /** Number of currently running requests */
    running: number
    
    /** Total pool size */
    size: number
}

export interface TestConfiguration {
    /** Number of requests to make */
    requests: number
    
    /** Delay between requests in milliseconds */
    delay: number
    
    /** Test endpoint URL */
    endpoint: string
    
    /** Show help message */
    help: boolean
}

export interface PoolConfiguration {
    /** Keep-alive timeout in milliseconds */
    keepAliveTimeout: number
    
    /** Maximum keep-alive timeout in milliseconds */
    keepAliveMaxTimeout: number
    
    /** Maximum number of concurrent connections */
    connections: number
    
    /** HTTP pipelining factor */
    pipelining: number
    
    /** Headers timeout in milliseconds */
    headersTimeout: number
    
    /** Body timeout in milliseconds */
    bodyTimeout: number
}

export interface TestScenarioResult {
    /** Scenario label */
    label: string
    
    /** Test metrics */
    metrics: TestMetrics
    
    /** Pool configuration used */
    poolConfig: PoolConfiguration
    
    /** Response times for individual requests */
    responseTimes: number[]
    
    /** Connection events count */
    connectionEvents: number
}

export interface ComparisonResult {
    /** Keep-alive scenario results */
    keepAlive: TestScenarioResult
    
    /** No keep-alive scenario results */
    noKeepAlive: TestScenarioResult
    
    /** Performance improvement percentage */
    performanceImprovement: number
    
    /** Connection reduction percentage */
    connectionReduction: number
    
    /** Overall conclusion */
    conclusion: {
        fasterResponseTimes: boolean
        reducedConnections: boolean
        effectiveReuse: boolean
        improvementPercentage: number
    }
}

/**
 * Event types for connection monitoring
 */
export type ConnectionEvent = 'connect' | 'disconnect'

export interface ConnectionEventData {
    /** Event type */
    type: ConnectionEvent
    
    /** Origin URL */
    origin: string
    
    /** Timestamp */
    timestamp: number
    
    /** Pool stats at time of event */
    stats: PoolStats
    
    /** Error if disconnect event */
    error?: Error
}

/**
 * Test runner options
 */
export interface TestRunnerOptions {
    /** Test configuration */
    config: TestConfiguration
    
    /** Custom endpoint for testing */
    customEndpoint?: string
    
    /** Enable verbose logging */
    verbose?: boolean
    
    /** Output format */
    outputFormat?: 'console' | 'json' | 'markdown'
}

/**
 * Performance benchmark thresholds
 */
export interface PerformanceThresholds {
    /** Minimum expected performance improvement percentage */
    minPerformanceImprovement: number
    
    /** Minimum expected connection reuse rate percentage */
    minReuseRate: number
    
    /** Maximum acceptable average response time in milliseconds */
    maxAverageResponseTime: number
    
    /** Maximum acceptable new connections for given request count */
    maxNewConnections: number
}

/**
 * Default performance thresholds for validation
 */
export const DEFAULT_PERFORMANCE_THRESHOLDS: PerformanceThresholds = {
    minPerformanceImprovement: 50, // 50% faster with keep-alive
    minReuseRate: 70,              // 70% connection reuse rate
    maxAverageResponseTime: 1000,  // 1 second max average
    maxNewConnections: 2           // Max 2 new connections for 10 requests
}

/**
 * Test validation result
 */
export interface ValidationResult {
    /** Whether test passed all thresholds */
    passed: boolean
    
    /** Individual threshold results */
    thresholds: {
        performanceImprovement: { passed: boolean; actual: number; expected: number }
        reuseRate: { passed: boolean; actual: number; expected: number }
        averageResponseTime: { passed: boolean; actual: number; expected: number }
        newConnections: { passed: boolean; actual: number; expected: number }
    }
    
    /** Overall score (0-100) */
    score: number
    
    /** Recommendations for improvement */
    recommendations: string[]
}
